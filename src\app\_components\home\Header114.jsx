"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Header114() {
  return (
    <section id="relume" className="relative px-[5%]">
      <div className="container flex max-h-[60rem] min-h-svh">
        <div className="py-16 md:py-24 lg:py-28">
          <div className="relative z-10 grid h-full auto-cols-fr grid-cols-1 gap-12 md:grid-cols-2 md:gap-20">
            <div className="flex flex-col justify-start md:justify-center">
              <h1 className="text-6xl font-bold text-text-alternative md:text-9xl lg:text-10xl">
                Medium length hero heading goes here
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button title="Button">Button</Button>
                <Button title="Button" variant="secondary-alt">
                  Button
                </Button>
              </div>
            </div>
            <div className="mx-[7.5%] flex flex-col justify-end">
              <p className="text-text-alternative md:text-md">
                Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven
                groot en klein. En dan hebben we het over duurzaamheid in al z’n
                vormen. Over impact maken. Op alle mogelijke manieren. Dat is de
                meerwaarde van ons ecosysteem. We rollen de mouwen op om samen
                lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken.
                Om jouw bedrijf een stem te geven, zodat je anderen kan
                inspireren. En om de betrokkenheid van jouw stakeholders te
                vergroten door ze slim én leuk te verbinden. Zorgen voor
                daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat
                is wat we doen!
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 z-0">
        <img
          src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
          className="size-full object-cover"
          alt="Relume placeholder image"
        />
        <div className="absolute inset-0 bg-black/50" />
      </div>
    </section>
  );
}
